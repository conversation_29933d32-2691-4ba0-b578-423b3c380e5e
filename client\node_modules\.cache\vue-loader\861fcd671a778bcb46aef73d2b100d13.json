{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\eslint-loader\\index.js??ref--14-0!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\PQEDashboard\\PQEOwnerDashboard.vue", "mtime": 1748910537759}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVuZGVyLCBzdGF0aWNSZW5kZXJGbnMgfSBmcm9tICIuL1BRRU93bmVyRGFzaGJvYXJkLnZ1ZT92dWUmdHlwZT10ZW1wbGF0ZSZpZD0yNWRiYTY4MCZzY29wZWQ9dHJ1ZSIKaW1wb3J0IHNjcmlwdCBmcm9tICIuL1BRRU93bmVyRGFzaGJvYXJkLnZ1ZT92dWUmdHlwZT1zY3JpcHQmbGFuZz1qcyIKZXhwb3J0ICogZnJvbSAiLi9QUUVPd25lckRhc2hib2FyZC52dWU/dnVlJnR5cGU9c2NyaXB0Jmxhbmc9anMiCmltcG9ydCBzdHlsZTAgZnJvbSAiLi9QUUVPd25lckRhc2hib2FyZC52dWU/dnVlJnR5cGU9c3R5bGUmaW5kZXg9MCZpZD0yNWRiYTY4MCZzY29wZWQ9dHJ1ZSZsYW5nPWNzcyIKCgovKiBub3JtYWxpemUgY29tcG9uZW50ICovCmltcG9ydCBub3JtYWxpemVyIGZyb20gIiEuLi8uLi8uLi9ub2RlX21vZHVsZXMvdnVlLWxvYWRlci9saWIvcnVudGltZS9jb21wb25lbnROb3JtYWxpemVyLmpzIgp2YXIgY29tcG9uZW50ID0gbm9ybWFsaXplcigKICBzY3JpcHQsCiAgcmVuZGVyLAogIHN0YXRpY1JlbmRlckZucywKICBmYWxzZSwKICBudWxsLAogICIyNWRiYTY4MCIsCiAgbnVsbAogIAopCgovKiBob3QgcmVsb2FkICovCmlmIChtb2R1bGUuaG90KSB7CiAgdmFyIGFwaSA9IHJlcXVpcmUoIkM6XFxVc2Vyc1xcRGFuaWVsUnVpejFcXERvY3VtZW50c1xcc3RhdGl0XzJcXGNsaWVudFxcbm9kZV9tb2R1bGVzXFx2dWUtaG90LXJlbG9hZC1hcGlcXGRpc3RcXGluZGV4LmpzIikKICBhcGkuaW5zdGFsbChyZXF1aXJlKCd2dWUnKSkKICBpZiAoYXBpLmNvbXBhdGlibGUpIHsKICAgIG1vZHVsZS5ob3QuYWNjZXB0KCkKICAgIGlmICghYXBpLmlzUmVjb3JkZWQoJzI1ZGJhNjgwJykpIHsKICAgICAgYXBpLmNyZWF0ZVJlY29yZCgnMjVkYmE2ODAnLCBjb21wb25lbnQub3B0aW9ucykKICAgIH0gZWxzZSB7CiAgICAgIGFwaS5yZWxvYWQoJzI1ZGJhNjgwJywgY29tcG9uZW50Lm9wdGlvbnMpCiAgICB9CiAgICBtb2R1bGUuaG90LmFjY2VwdCgiLi9QUUVPd25lckRhc2hib2FyZC52dWU/dnVlJnR5cGU9dGVtcGxhdGUmaWQ9MjVkYmE2ODAmc2NvcGVkPXRydWUiLCBmdW5jdGlvbiAoKSB7CiAgICAgIGFwaS5yZXJlbmRlcignMjVkYmE2ODAnLCB7CiAgICAgICAgcmVuZGVyOiByZW5kZXIsCiAgICAgICAgc3RhdGljUmVuZGVyRm5zOiBzdGF0aWNSZW5kZXJGbnMKICAgICAgfSkKICAgIH0pCiAgfQp9CmNvbXBvbmVudC5vcHRpb25zLl9fZmlsZSA9ICJzcmMvdmlld3MvUFFFRGFzaGJvYXJkL1BRRU93bmVyRGFzaGJvYXJkLnZ1ZSIKZXhwb3J0IGRlZmF1bHQgY29tcG9uZW50LmV4cG9ydHM="}]}