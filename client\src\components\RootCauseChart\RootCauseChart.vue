

<template>
  <div ref='chartHolder'>
  </div>
</template>

<script>
import Vue from 'vue';
import '@carbon/charts/styles.css'; // Import Carbon Charts styles
import chartsVue from '@carbon/charts-vue';
import { StackedBarChart } from '@carbon/charts'; // Import StackedBarChart

Vue.use(chartsVue);

export default {
  name: 'RootCauseChart',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: true // Default is loading true
    },
    title: {
      type: String,
      default: 'Root Cause Categories'
    },
    eventType: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      options:{
      title: this.title,
        axes: {
          left: {
            title: 'Fail Rate (%)',
            mapsTo: 'value',
            stacked: true,
            
          },
          bottom: {
            title: 'Month',
            mapsTo: 'key',
            scaleType: 'labels'
          }
        },
        legend: {
          alignment: 'center',
          enabled: true
        },
        toolbar: {
          enabled: true
        },
        tooltip: {
          enabled: true
        },
        height: this.height,
        theme: 'g100',
        stacked: true,
        animations: true,
        bars: {
          width: 50
        },
        data: {
          // groupMapsTo: 'group',
          loading: this.loading
        }
      }
    }
    },
  mounted() {
    const chartHolder = this.$refs.chartHolder;
    console.log(chartHolder);

    this.chart = new StackedBarChart(chartHolder, {
      data: this.data,
      options: this.options,
    });

    this.chart.services.events.addEventListener('bar-click', (e) => {
      this.eventType(e);
    });
  },
  watch: {
    loading(newVal) {
      // Update loading state dynamically when the prop changes
      this.options.data.loading = newVal;
      this.chart.model.setOptions(this.options);
    },
    data(newData) {
        this.chart.model.setData(newData);
      
    }
  },
};
</script>

<style>
button[aria-label="Show as table"] {
  display: none;
  pointer-events: none;
}
div.toolbar-control.bx--overflow-menu[aria-label="Show as table"] {
  display: none;
}
</style>
